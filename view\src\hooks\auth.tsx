import {
	type ReactNode,
	createContext,
	useContext,
	useEffect,
	useState,
} from "react";

import { api } from "./../service/api";

type signOut = () => void;

interface AuthProviderProps {
	children: ReactNode;
}

interface LoginData {
	username: string;
	password: string;
}

interface AuthContextData {
	signIn: (data: LoginData) => Promise<void>;
	signOut: signOut;
	user: User | null;
}

interface User {
	id: number;
	username: string;
	userType: "staff" | "admin";
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string;
		cnpj: string;
		personType: "individual" | "legal";
	};
}

export const AuthContext = createContext<AuthContextData | null>(null);

function AuthProvider({ children }: AuthProviderProps) {
	const [user, setUser] = useState<{ user: User | null }>({ user: null });

	async function signIn({ username, password }: LoginData) {
		try {
			console.log("Tentando fazer login com:", { username });
			const response = await api.post("/users/login", { username, password });

			const user = response.data;
			console.log("Resposta do login:", user);

			localStorage.setItem("@managermalhas:user", JSON.stringify(user));
			console.log("Usuário salvo no localStorage");

			setUser({ user: user });
			console.log("Estado do usuário atualizado");
		} catch (error: any) {
			console.error("Erro no login:", error);
			if (error.response) {
				alert(error.response.data.error);
			} else {
				alert("Nome de usuário ou senha incorreto(s)!");
			}
		}
	}

	async function signOut() {
		localStorage.removeItem("@managermalhas:user");

		setUser({ user: null });
	}

	useEffect(() => {
		console.log("Verificando usuário no localStorage...");
		const storedUser = localStorage.getItem("@managermalhas:user");
		console.log("Usuário armazenado:", storedUser);

		if (storedUser) {
			try {
				const user: User = JSON.parse(storedUser);
				console.log("Usuário parseado:", user);
				setUser({ user });
				console.log("Estado do usuário carregado do localStorage");
			} catch (error) {
				console.error("Erro ao parsear usuário do localStorage:", error);
				localStorage.removeItem("@managermalhas:user");
			}
		}
	}, []);

	return (
		<AuthContext.Provider value={{ signIn, signOut, user: user?.user || null }}>
			{children}
		</AuthContext.Provider>
	);
}

function useAuth(): AuthContextData {
	const context = useContext(AuthContext);

	if (!context) {
		throw new Error("useAuth deve ser usado dentro de um AuthProvider");
	}

	return context;
}

export { AuthProvider, useAuth };

