import { useEffect, useState } from "react";

import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

import { api } from "../../../service/api";

import { IoClose } from "react-icons/io5";
import { AlertModal } from "../../../components/AlertModal";

interface Garment {
	id: number;
	name: string;
	refcode: string;
	price: string | number;
	size: string;
	color: string;
}

interface User {
	id: number;
	username: string;
	password: string;
	userType: "staff" | "admin";
	personData: {
		personId: number;
		fullname: string;
		contact: string;
		cpf: string;
		cnpj: string;
		personType: "individual" | "private";
	};
}

export function ModelList() {
	const [garments, setGarments] = useState<Garment[]>([]);
	const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
	const [selectedGarment, setSelectedGarment] = useState<Garment | null>(null);

	const [modelName, setModelName] = useState("");
	const [modelCode, setModelCode] = useState("");
	const [modelPrice, setModelPrice] = useState<string | number>("");
	const [modelColor, setModelColor] = useState("");
	const [modelSize, setModelSize] = useState("");

	const [searchTerm, setSearchTerm] = useState<string>("");

	let user: User | null = null;

	const storedUser = localStorage.getItem("@managermalhas:user");

	if (storedUser) {
		try {
			user = JSON.parse(storedUser) as User;
		} catch (error) {
			console.error("Erro ao fazer o parse do usuário:", error);
		}
	}

	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">("success");
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const openModal = (garment: Garment): void => {
		setSelectedGarment(garment);
		setModelName(garment.name);
		setModelCode(garment.refcode);
		setModelPrice(garment.price);
		setModelColor(garment.color);
		setModelSize(garment.size);
		setIsModalOpen(true);
		document.body.style.overflow = "hidden";
	};

	const closeModal = (): void => {
		setSelectedGarment(null);
		setIsModalOpen(false);
		document.body.style.overflow = "";
	};

	function formatPrice(value: number): string {
		return new Intl.NumberFormat("pt-BR", {
			style: "currency",
			currency: "BRL",
		}).format(value);
	}

	async function handleUpdateModel() {
		if (!modelName) {
			setAlertMessage("O nome do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelCode) {
			setAlertMessage("O código do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelPrice) {
			setAlertMessage("O preço do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelColor) {
			setAlertMessage("A cor do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelSize) {
			setAlertMessage("O tamanho do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			const updatedGarment = {
				name: modelName,
				refcode: modelCode.toUpperCase(),
				price: Number.parseFloat(modelPrice.toString()),
				size: modelSize,
				color: modelColor,
			};

			await api.patch(`/garments/${selectedGarment?.id}`, updatedGarment);

			setGarments((prevGarments) =>
				prevGarments.map((garment) =>
					garment.id === selectedGarment?.id
						? {
							...garment,
							...updatedGarment,
						}
						: garment,
				),
			);

			setAlertMessage("Modelo atualizado com sucesso!");
			setAlertType("success");
			setAlertCallback(() => () => closeModal());
			setIsAlertModalOpen(true);
		} catch (error) {
			setAlertMessage("Não foi possível atualizar o modelo...");
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	async function handleDeleteModel(id: number) {
		setAlertMessage("Tem certeza que deseja excluir esse modelo?");
		setAlertType("confirm");
		setAlertCallback(() => async () => {
			try {
				await api.delete(`/garments/${id}`);
				setGarments([]);
				setAlertMessage("Modelo apagado com sucesso!");
				setAlertType("success");
				setTimeout(() => setIsAlertModalOpen(false), 1500);
			} catch (error) {
				setAlertMessage("Não foi possível deletar o modelo.");
				setAlertType("error");
				setTimeout(() => setIsAlertModalOpen(false), 1500);
			}
		});
		setIsAlertModalOpen(true);
	}

	useEffect(() => {
		if (isModalOpen) {
			const focusableElements = document.querySelectorAll(
				'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
			);
			const firstElement = focusableElements[0] as HTMLElement;
			const lastElement = focusableElements[
				focusableElements.length - 1
			] as HTMLElement;

			const trapFocus = (e: KeyboardEvent) => {
				if (e.key === "Tab") {
					if (document.activeElement === lastElement && !e.shiftKey) {
						e.preventDefault();
						firstElement.focus();
					} else if (document.activeElement === firstElement && e.shiftKey) {
						e.preventDefault();
						lastElement.focus();
					}
				}
			};

			firstElement?.focus();
			document.addEventListener("keydown", trapFocus);

			return () => {
				document.removeEventListener("keydown", trapFocus);
			};
		}
	}, [isModalOpen]);

	useEffect(() => {
		async function getGarment() {
			try {
				const response = await api.get("/garments");
				const allGarments = response.data;

				if (searchTerm.trim()) {
					const filteredGarments = allGarments.filter((garment: Garment) =>
						garment.name.toLowerCase().includes(searchTerm.toLowerCase()),
					);
					setGarments(filteredGarments);
				} else {
					setGarments(allGarments);
				}
			} catch (error) {
				console.error("Erro ao buscar modelos:", error);
			}
		}
		getGarment();
	}, [searchTerm]);

	return (
		<>
			<div className="min-h-screen flex flex-col">
				<Header pagename={"Lista de Modelos"} href={"/"} $logout={false} />

				<main className="flex-grow flex flex-col items-center px-4">
					<h2 className="text-2xl text-left mt-6">Lista de modelos:</h2>
					<input
						type="text"
						placeholder="Buscar por nome do modelo..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="border border-gray-300 rounded-md h-10 my-6 w-2/3 px-2 text-lg mx-auto"
					/>
					<div
						id="modelList"
						className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 mb-5"
					>
						{garments.length > 0 ? (
							garments.map((garment) => (
								<div
									key={garment.id}
									className="model relative flex flex-col gap-0.5 max-w-72 py-5 px-8 items-center border border-gray-400 rounded"
								>
									{user?.userType !== "admin" ? (
										<></>
									) : (
										<IoClose
											className="text-red-600 absolute top-2 right-3 cursor-pointer text-2xl"
											onClick={() => {
												handleDeleteModel(garment.id);
											}}
										/>
									)}

									<span className="font-inter text-lg">
										Modelo: #{garment.refcode}
									</span>
									<span className="font-inter text-base truncate">
										Nome: {garment.name}
									</span>
									<span className="font-inter text-base">
										Cor: {garment.color}
									</span>
									<span className="font-inter text-base">
										Tamanho: {garment.size}
									</span>
									<p className="flex flex-col items-center font-inter text-base">
										Preço Unitário:
										<span>
											<span>{formatPrice(Number(garment.price))}</span>
										</span>
									</p>

									<button
										type="button"
										className="border border-gray-400 rounded bg bg-blue-200 px-8 py-0.5 mt-2 font-roboto text-lg"
										onClick={() => openModal(garment)}
									>
										Detalhes
									</button>
								</div>
							))
						) : (
							<div className="col-span-full text-center text-xl text-gray-500 py-8">
								Nenhum modelo encontrado com o termo "{searchTerm}"
							</div>
						)}
					</div>

					{/* Modal */}
					{isModalOpen && selectedGarment && (
						<div className="modal-overlay fixed inset-0 flex items-center justify-center backdrop-blur bg-black bg-opacity-50 px-1">
							<form
								className="modal-content bg-white p-5 rounded relative shadow-lg max-w-sm w-full"
								onSubmit={(e) => {
									e.preventDefault();
									handleUpdateModel();
								}}
							>
								<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4">
									<IoClose
										onClick={closeModal}
										className="text-red-700 absolute top-7 right-7 cursor-pointer text-2xl"
									/>

									<div className="input-wrapper flex flex-col">
										<label htmlFor="nwmodel-name" className="mb-1">
											Nome do novo Modelo:
										</label>
										<input
											type="text"
											id="nwmodel-name"
											className="text-sm border border-gray-300 rounded px-2 py-1 w-64 sm:w-full"
											value={modelName}
											onChange={(e) => setModelName(e.target.value)}
										/>
									</div>

									<div className="line-wrapper flex justify-between gap-4 w-64 sm:w-full">
										<div className="input-wrapper flex flex-col flex-grow">
											<label htmlFor="model-code" className="mb-1 truncate">
												Código do modelo:
											</label>
											<input
												type="text"
												id="model-code"
												className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
												placeholder="Apenas código"
												value={modelCode}
												onChange={(e) => setModelCode(e.target.value)}
											/>
										</div>

										<div className="input-wrapper flex flex-col flex-grow">
											<label htmlFor="model-price" className="mb-1">
												Preço:
											</label>
											<input
												type="number"
												min={0}
												step="0.01"
												id="model-price"
												className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
												value={modelPrice}
												onChange={(e) => setModelPrice(e.target.value)}
											/>
										</div>
									</div>

									<div className="line-wrapper flex justify-between gap-4 w-64 sm:w-full">
										<div className="input-wrapper flex flex-col flex-grow">
											<label htmlFor="model-color" className="mb-1">
												Cor:
											</label>
											<input
												type="text"
												id="model-color"
												className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
												value={modelColor}
												onChange={(e) => setModelColor(e.target.value)}
											/>
										</div>

										<div className="input-wrapper flex flex-col flex-grow">
											<label htmlFor="model-size" className="mb-1">
												Tamanho:
											</label>
											<input
												type="text"
												id="model-size"
												className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
												value={modelSize}
												onChange={(e) => setModelSize(e.target.value)}
											/>
										</div>
									</div>

									<button
										type="submit"
										className="bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
									>
										Confirmar Atualização
									</button>
								</fieldset>
							</form>
						</div>
					)}
				</main>

				<Footer />
			</div>
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => {
					setIsAlertModalOpen(false);
					if (alertType === "success" && alertCallback) {
						alertCallback();
					}
				}}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</>
	);
}
